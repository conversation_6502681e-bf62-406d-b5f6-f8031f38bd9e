{% extends "base.html" %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="staff-container">
    <h1>User Management</h1>

    <!-- Search and Filter Section -->
    <div class="search-filter-section" style="margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
        <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
            <div style="flex: 1; min-width: 200px;">
                <input type="text" id="searchInput" placeholder="Search users..." style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px; width: 100%;" onkeyup="filterUsers()">
            </div>
            <div style="flex: 0 0 auto;">
                <select id="roleFilter" style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px;" onchange="filterUsers()">
                    <option value="">All Roles</option>
                    {% for role in roles %}
                    <option value="{{ role.name }}">{{ role.display_name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div style="flex: 0 0 auto;">
                <select id="statusFilter" style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px;" onchange="filterUsers()">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
            <div style="flex: 0 0 auto;">
                <button type="button" onclick="clearFilters()" style="padding: 8px 12px; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">Clear</button>
            </div>
        </div>
    </div>

    <!-- Add User Form -->
    <div class="inventory-actions">
        <div class="inventory-search">
            <form method="post" style="display: flex; flex-wrap: wrap; gap: 10px; align-items: end; width: 100%;">
                <div style="flex: 1; min-width: 140px;">
                    <label for="username" style="display: block; margin-bottom: 5px; font-weight: bold;">Username:</label>
                    <input type="text" id="username" name="username" required style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px; width: 100%;">
                </div>
                <div style="flex: 1; min-width: 140px;">
                    <label for="full_name" style="display: block; margin-bottom: 5px; font-weight: bold;">Full Name:</label>
                    <input type="text" id="full_name" name="full_name" style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px; width: 100%;">
                </div>
                <div style="flex: 1; min-width: 140px;">
                    <label for="email" style="display: block; margin-bottom: 5px; font-weight: bold;">Email:</label>
                    <input type="email" id="email" name="email" style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px; width: 100%;">
                </div>
                <div style="flex: 1.2; min-width: 180px;">
                    <label for="password" style="display: block; margin-bottom: 5px; font-weight: bold;">Password:</label>
                    <div style="display: flex; gap: 5px;">
                        <input type="password" id="password" name="password" required style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px; min-width: 140px; flex: 1;">
                        <button type="button" onclick="generatePassword()" style="padding: 8px 12px; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; white-space: nowrap;" title="Generate Password">Gen</button>
                    </div>
                </div>
                <div style="flex: 0.8; min-width: 110px;">
                    <label for="role" style="display: block; margin-bottom: 5px; font-weight: bold;">Role:</label>
                    <select id="role" name="role" required style="padding: 8px 12px; border: 1px solid #ccc; border-radius: 5px; width: 100%;">
                        {% for role in roles %}
                        <option value="{{ role.name }}">{{ role.display_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div style="flex: 0 0 auto; margin-left: 10px;">
                    <label style="display: block; margin-bottom: 5px; font-weight: bold; visibility: hidden;">Action:</label>
                    <button type="submit" style="padding: 8px 16px; background-color: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; min-width: 100px;">Add User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Users Table -->
    <div class="inventory-list">
        <table style="width: 100%; border-collapse: collapse; border: 1px solid #aaa; font-weight: 400;">
            <thead>
                <tr>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">ID</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Username</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Full Name</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Email</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Role</th>
                    <th style="padding: 12px; text-align: left; border-bottom: 1px solid #888; background-color: #f5f5f5;">Last Login</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #888; background-color: #f5f5f5;">Status</th>
                    <th style="padding: 12px; text-align: center; border-bottom: 1px solid #888; background-color: #f5f5f5;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">{{ user.id }}</td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="username-display">{{ user.username }}</span>
                        <input type="text" class="username-edit" value="{{ user.username }}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                    </td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="fullname-display">{{ user.full_name or '-' }}</span>
                        <input type="text" class="fullname-edit" value="{{ user.full_name or '' }}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                    </td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="email-display">{{ user.email or '-' }}</span>
                        <input type="email" class="email-edit" value="{{ user.email or '' }}" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                    </td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        <span class="role-display">
                            {% set role_colors = {
                                'super_admin': '#dc3545',
                                'admin': '#fd7e14',
                                'manager': '#6f42c1',
                                'staff': '#28a745',
                                'sales': '#17a2b8',
                                'clerk': '#6c757d',
                                'cashier': '#ffc107'
                            } %}
                            {% set role_obj = roles|selectattr('name', 'equalto', user.role)|first %}
                            <span style="background-color: {{ role_colors.get(user.role, '#6c757d') }}; color: white; padding: 4px 8px; border-radius: 12px; font-size: 12px;">
                                {{ role_obj.display_name if role_obj else user.role|title }}
                            </span>
                        </span>
                        <select class="role-edit" style="display: none; padding: 4px; border: 1px solid #ccc; border-radius: 3px;">
                            {% for role in roles %}
                            <option value="{{ role.name }}" {% if user.role == role.name %}selected{% endif %}>{{ role.display_name }}</option>
                            {% endfor %}
                        </select>
                    </td>
                    <td style="padding: 12px; text-align: left; border-bottom: 1px solid #888;">
                        {% if user.last_login %}
                            {{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else '-' }}
                        {% else %}
                            <span style="color: #6c757d;">Never</span>
                        {% endif %}
                    </td>
                    <td style="padding: 12px; text-align: center; border-bottom: 1px solid #888;">
                        {% if user.is_active %}
                            <span style="background-color: #28a745; color: white; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Active</span>
                        {% else %}
                            <span style="background-color: #dc3545; color: white; padding: 2px 6px; border-radius: 8px; font-size: 11px;">Inactive</span>
                        {% endif %}
                        {% if user.force_password_change %}
                            <br><span style="background-color: #ffc107; color: black; padding: 2px 6px; border-radius: 8px; font-size: 10px; margin-top: 2px; display: inline-block;">Pwd Reset Required</span>
                        {% endif %}
                    </td>
                    <td style="padding: 12px; text-align: center; border-bottom: 1px solid #888;">
                        <div class="action-buttons">
                            <button class="edit-btn" onclick="editUser({{ user.id }})" style="background-color: #ffc107; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;">Edit</button>
                            <button class="save-btn" onclick="saveUser({{ user.id }})" style="display: none; background-color: #28a745; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;">Save</button>
                            <button class="cancel-btn" onclick="cancelEdit({{ user.id }})" style="display: none; background-color: #6c757d; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;">Cancel</button>
                            <button onclick="resetPassword({{ user.id }}, '{{ user.username }}')" style="background-color: #17a2b8; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;" title="Reset Password">Reset Pwd</button>
                            {% if user.id != session.user_id %}
                            <button onclick="forcePasswordChange({{ user.id }}, '{{ user.username }}')" style="background-color: #6f42c1; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;" title="Force Password Change">Force Pwd</button>
                            <button onclick="toggleUserStatus({{ user.id }}, '{{ user.username }}', {{ 'true' if user.is_active else 'false' }})" style="background-color: {% if user.is_active %}#ffc107{% else %}#28a745{% endif %}; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px; margin-right: 5px;" title="{% if user.is_active %}Deactivate{% else %}Activate{% endif %} User">{% if user.is_active %}Deactivate{% else %}Activate{% endif %}</button>
                            <button onclick="deleteUser({{ user.id }}, '{{ user.username }}')" style="background-color: #dc3545; color: white; border: none; padding: 4px 8px; border-radius: 3px; cursor: pointer; font-size: 12px;">Delete</button>
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>

<!-- Message Container for Notifications -->
<div id="message-container"></div>

{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/staff_notifications.css') }}">
<style>
    .staff-container {
        width: 100%;
        margin: 0 auto;
        padding: 20px;
    }

    .inventory-actions {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        margin: 20px 0;
        gap: 15px;
    }

    .inventory-search {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;
        max-width: 1000px;
    }

    .inventory-list table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #aaa;
        font-weight: 400;
    }

    .inventory-list th,
    .inventory-list td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #888;
    }

    .inventory-list th {
        background-color: #f5f5f5;
        font-weight: bold;
    }

    .inventory-list tbody tr:hover {
        background-color: #f8f9fa;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
        justify-content: center;
        align-items: center;
    }

    .action-buttons button {
        transition: background-color 0.2s;
    }

    .action-buttons button:hover {
        opacity: 0.8;
    }
</style>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/staff_messages.js') }}"></script>
<script>
function editUser(userId) {
    const row = document.querySelector(`tr:has(button[onclick*="${userId}"])`);

    // Hide display elements and show edit elements
    row.querySelector('.username-display').style.display = 'none';
    row.querySelector('.username-edit').style.display = 'inline';
    row.querySelector('.fullname-display').style.display = 'none';
    row.querySelector('.fullname-edit').style.display = 'inline';
    row.querySelector('.email-display').style.display = 'none';
    row.querySelector('.email-edit').style.display = 'inline';
    row.querySelector('.role-display').style.display = 'none';
    row.querySelector('.role-edit').style.display = 'inline';

    // Hide edit button, show save/cancel buttons
    row.querySelector('.edit-btn').style.display = 'none';
    row.querySelector('.save-btn').style.display = 'inline';
    row.querySelector('.cancel-btn').style.display = 'inline';
}

function cancelEdit(userId) {
    const row = document.querySelector(`tr:has(button[onclick*="${userId}"])`);

    // Show display elements and hide edit elements
    row.querySelector('.username-display').style.display = 'inline';
    row.querySelector('.username-edit').style.display = 'none';
    row.querySelector('.fullname-display').style.display = 'inline';
    row.querySelector('.fullname-edit').style.display = 'none';
    row.querySelector('.email-display').style.display = 'inline';
    row.querySelector('.email-edit').style.display = 'none';
    row.querySelector('.role-display').style.display = 'inline';
    row.querySelector('.role-edit').style.display = 'none';

    // Show edit button, hide save/cancel buttons
    row.querySelector('.edit-btn').style.display = 'inline';
    row.querySelector('.save-btn').style.display = 'none';
    row.querySelector('.cancel-btn').style.display = 'none';

    // Reset values
    const originalUsername = row.querySelector('.username-display').textContent;
    const originalFullname = row.querySelector('.fullname-display').textContent;
    const originalEmail = row.querySelector('.email-display').textContent;
    row.querySelector('.username-edit').value = originalUsername;
    row.querySelector('.fullname-edit').value = originalFullname === '-' ? '' : originalFullname;
    row.querySelector('.email-edit').value = originalEmail === '-' ? '' : originalEmail;
}

function saveUser(userId) {
    const row = document.querySelector(`tr:has(button[onclick*="${userId}"])`);
    const username = row.querySelector('.username-edit').value.trim();
    const fullName = row.querySelector('.fullname-edit').value.trim();
    const email = row.querySelector('.email-edit').value.trim();
    const role = row.querySelector('.role-edit').value;

    if (!username) {
        showMessage('Username cannot be empty', 'error');
        return;
    }

    fetch(`/auth/api/staff/users/${userId}/update`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            username: username,
            full_name: fullName || null,
            email: email || null,
            role: role
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage(data.message, 'success');
            // Update display values
            row.querySelector('.username-display').textContent = username;
            row.querySelector('.fullname-display').textContent = fullName || '-';
            row.querySelector('.email-display').textContent = email || '-';

            // Update role badge
            const roleDisplay = row.querySelector('.role-display span');
            if (role === 'super_admin') {
                roleDisplay.textContent = 'Super Admin';
                roleDisplay.style.backgroundColor = '#dc3545';
            } else if (role === 'admin') {
                roleDisplay.textContent = 'Admin';
                roleDisplay.style.backgroundColor = '#fd7e14';
            } else {
                roleDisplay.textContent = 'Staff';
                roleDisplay.style.backgroundColor = '#28a745';
            }

            cancelEdit(userId);
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error updating user', 'error');
    });
}

function deleteUser(userId, username) {
    if (confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
        fetch(`/auth/api/staff/users/${userId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                // Remove the row from table
                const row = document.querySelector(`tr:has(button[onclick*="${userId}"])`);
                row.remove();
            } else {
                showMessage(data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Error deleting user', 'error');
        });
    }
}

function resetPassword(userId, username) {
    showPasswordResetModal(userId, username);
}

function showPasswordResetModal(userId, username) {
    // Create modal HTML
    const modalHTML = `
        <div id="passwordResetModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); max-width: 500px; width: 90%;">
                <div style="text-align: center; margin-bottom: 1.5rem;">
                    <i class="fas fa-key" style="font-size: 2.5rem; color: #17a2b8; margin-bottom: 1rem;"></i>
                    <h3 style="color: #333; margin: 0 0 0.5rem 0;">Reset Password</h3>
                    <p style="color: #666; margin: 0;">Reset password for user: <strong>${username}</strong></p>
                </div>

                <div style="margin-bottom: 1.5rem;">
                    <label for="newPasswordInput" style="display: block; margin-bottom: 0.5rem; color: #333; font-weight: 500;">New Password:</label>
                    <div style="display: flex; gap: 10px;">
                        <input type="text" id="newPasswordInput" placeholder="Leave empty to generate temporary password" style="flex: 1; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; font-size: 1rem;">
                        <button type="button" onclick="generateModalPassword()" style="padding: 0.75rem 1rem; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer; white-space: nowrap;">Generate</button>
                    </div>
                    <small style="color: #666; font-size: 0.85rem; margin-top: 0.5rem; display: block;">Leave empty to auto-generate a secure temporary password</small>
                </div>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" onclick="closePasswordResetModal()" style="padding: 0.75rem 1.5rem; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">Cancel</button>
                    <button type="button" onclick="confirmPasswordReset(${userId}, '${username}')" style="padding: 0.75rem 1.5rem; background-color: #17a2b8; color: white; border: none; border-radius: 5px; cursor: pointer;">Reset Password</button>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Focus on input
    document.getElementById('newPasswordInput').focus();

    // Close modal when clicking outside
    document.getElementById('passwordResetModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closePasswordResetModal();
        }
    });
}

function generateModalPassword() {
    const chars = 'ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789';
    let password = '';
    for (let i = 0; i < 12; i++) {
        password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    document.getElementById('newPasswordInput').value = password;
}

function closePasswordResetModal() {
    const modal = document.getElementById('passwordResetModal');
    if (modal) {
        modal.remove();
    }
}

function confirmPasswordReset(userId, username) {
    const newPassword = document.getElementById('newPasswordInput').value.trim();

    fetch(`/auth/api/staff/users/${userId}/reset-password`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            new_password: newPassword
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closePasswordResetModal();
            showMessage(data.message, 'success');
            if (data.temporary_password) {
                showPasswordResultModal(data.temporary_password, username);
            }
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error resetting password', 'error');
    });
}

function showPasswordResultModal(password, username) {
    const modalHTML = `
        <div id="passwordResultModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); max-width: 500px; width: 90%;">
                <div style="text-align: center; margin-bottom: 1.5rem;">
                    <i class="fas fa-check-circle" style="font-size: 2.5rem; color: #28a745; margin-bottom: 1rem;"></i>
                    <h3 style="color: #333; margin: 0 0 0.5rem 0;">Password Reset Successful</h3>
                    <p style="color: #666; margin: 0;">Temporary password for user: <strong>${username}</strong></p>
                </div>

                <div style="background: #f8f9fa; padding: 1rem; border-radius: 5px; margin-bottom: 1.5rem; border-left: 4px solid #28a745;">
                    <label style="display: block; margin-bottom: 0.5rem; color: #333; font-weight: 500;">Temporary Password:</label>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="text" id="tempPasswordDisplay" value="${password}" readonly style="flex: 1; padding: 0.75rem; border: 2px solid #ddd; border-radius: 5px; font-size: 1rem; font-family: monospace; background: white;">
                        <button type="button" onclick="copyPassword()" style="padding: 0.75rem 1rem; background-color: #17a2b8; color: white; border: none; border-radius: 5px; cursor: pointer;">Copy</button>
                    </div>
                    <small style="color: #666; font-size: 0.85rem; margin-top: 0.5rem; display: block;">⚠️ Please save this password securely and share it with the user through a secure channel.</small>
                </div>

                <div style="text-align: center;">
                    <button type="button" onclick="closePasswordResultModal()" style="padding: 0.75rem 1.5rem; background-color: #28a745; color: white; border: none; border-radius: 5px; cursor: pointer;">Got It</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

function copyPassword() {
    const passwordInput = document.getElementById('tempPasswordDisplay');
    passwordInput.select();
    navigator.clipboard.writeText(passwordInput.value).then(() => {
        showMessage('Password copied to clipboard!', 'success');
    }).catch(() => {
        // Fallback for older browsers
        document.execCommand('copy');
        showMessage('Password copied to clipboard!', 'success');
    });
}

function closePasswordResultModal() {
    const modal = document.getElementById('passwordResultModal');
    if (modal) {
        modal.remove();
    }
}

function generatePassword() {
    fetch('/auth/api/staff/users/generate-password')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('password').value = data.password;
            document.getElementById('password').type = 'text'; // Show generated password
            showMessage('Password generated successfully', 'success');
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error generating password', 'error');
    });
}

function filterUsers() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const roleFilter = document.getElementById('roleFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const username = row.cells[1].textContent.toLowerCase();
        const fullName = row.cells[2].textContent.toLowerCase();
        const email = row.cells[3].textContent.toLowerCase();
        const role = row.cells[4].textContent.toLowerCase();
        const status = row.cells[6].textContent.toLowerCase();

        // Check search term
        const matchesSearch = !searchTerm ||
            username.includes(searchTerm) ||
            fullName.includes(searchTerm) ||
            email.includes(searchTerm);

        // Check role filter
        const matchesRole = !roleFilter || role.includes(roleFilter.toLowerCase());

        // Check status filter
        const matchesStatus = !statusFilter ||
            (statusFilter === 'active' && status.includes('active')) ||
            (statusFilter === 'inactive' && status.includes('inactive'));

        // Show/hide row based on all filters
        if (matchesSearch && matchesRole && matchesStatus) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('roleFilter').value = '';
    document.getElementById('statusFilter').value = '';
    filterUsers();
}

function toggleUserStatus(userId, username, currentStatus) {
    const newStatus = !currentStatus;
    const action = newStatus ? 'activate' : 'deactivate';

    if (confirm(`Are you sure you want to ${action} user "${username}"?`)) {
        fetch(`/auth/api/staff/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                is_active: newStatus
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage(data.message, 'success');
                // Refresh the page to update the UI
                window.location.reload();
            } else {
                showMessage(data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showMessage('Error updating user status', 'error');
        });
    }
}

function forcePasswordChange(userId, username) {
    showForcePasswordChangeModal(userId, username);
}

function showForcePasswordChangeModal(userId, username) {
    const modalHTML = `
        <div id="forcePasswordModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
            <div style="background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3); max-width: 500px; width: 90%;">
                <div style="text-align: center; margin-bottom: 1.5rem;">
                    <i class="fas fa-exclamation-triangle" style="font-size: 2.5rem; color: #ffc107; margin-bottom: 1rem;"></i>
                    <h3 style="color: #333; margin: 0 0 0.5rem 0;">Force Password Change</h3>
                    <p style="color: #666; margin: 0;">User: <strong>${username}</strong></p>
                </div>

                <div style="background: #fff3cd; padding: 1rem; border-radius: 5px; margin-bottom: 1.5rem; border-left: 4px solid #ffc107;">
                    <p style="margin: 0; color: #856404; font-size: 0.95rem;">
                        <strong>⚠️ This action will:</strong><br>
                        • Require the user to change their password on next login<br>
                        • Prevent access until password is changed<br>
                        • Cannot be undone automatically
                    </p>
                </div>

                <p style="color: #666; margin-bottom: 1.5rem; text-align: center;">
                    Are you sure you want to force a password change for this user?
                </p>

                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button type="button" onclick="closeForcePasswordModal()" style="padding: 0.75rem 1.5rem; background-color: #6c757d; color: white; border: none; border-radius: 5px; cursor: pointer;">Cancel</button>
                    <button type="button" onclick="confirmForcePasswordChange(${userId}, '${username}')" style="padding: 0.75rem 1.5rem; background-color: #ffc107; color: #212529; border: none; border-radius: 5px; cursor: pointer; font-weight: 500;">Force Change</button>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Close modal when clicking outside
    document.getElementById('forcePasswordModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeForcePasswordModal();
        }
    });
}

function closeForcePasswordModal() {
    const modal = document.getElementById('forcePasswordModal');
    if (modal) {
        modal.remove();
    }
}

function confirmForcePasswordChange(userId, username) {
    fetch(`/auth/api/staff/users/${userId}/force-password-change`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            closeForcePasswordModal();
            showMessage(data.message, 'success');
            // Refresh the page to update the UI
            window.location.reload();
        } else {
            showMessage(data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showMessage('Error forcing password change', 'error');
    });
}
</script>
{% endblock %}
